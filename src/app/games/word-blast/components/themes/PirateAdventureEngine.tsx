'use client';

import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { WordBlastEngineProps } from '../WordBlastEngine';
import { WordObject, ParticleEffect, BaseThemeEngine } from '../BaseThemeEngine';

// Updated PirateShip interface
interface PirateShip extends WordObject {
  shipType: 'galleon' | 'frigate' | 'sloop';
  sailsUp: boolean;
  treasureValue: number;
  weathered: number;
  floating: boolean;
  clicked: boolean; // Added: To control permanent disappearance
  feedbackStatus?: 'incorrect'; // Added: For temporary visual feedback on incorrect clicks
}

interface CannonBall {
  id: string;
  x: number;
  y: number;
  targetX: number;
  targetY: number;
  progress: number;
}

// Define a maximum number of active ships to maintain game flow
const MAX_ACTIVE_SHIPS = 8;

export default function PirateAdventureEngine(props: WordBlastEngineProps) {
  const {
    currentChallenge,
    challenges,
    onCorrectAnswer,
    onIncorrectAnswer,
    onChallengeComplete,
    isPaused,
    gameActive,
    difficulty,
    playSFX
  } = props;

  const [pirateShips, setPirateShips] = useState<PirateShip[]>([]);
  const [particles, setParticles] = useState<ParticleEffect[]>([]);
  const [cannonBalls, setCannonBalls] = useState<CannonBall[]>([]);
  const [wordsCollected, setWordsCollected] = useState<string[]>([]);
  const [challengeProgress, setChallengeProgress] = useState(0);
  const [currentWordIndex, setCurrentWordIndex] = useState(0);
  
  const containerRef = useRef<HTMLDivElement>(null);
  const animationRef = useRef<number>();
  const themeEngine = useRef(new PirateAdventureThemeEngine());

  // Helper function to create a single new ship object
  const createSingleShip = (existingShips: PirateShip[], initialYOffset: number = 0) => {
    if (!currentChallenge) return null;

    const correctWords = currentChallenge.words;
    const decoys = themeEngine.current.generateDecoys(correctWords, challenges, difficulty);
    const allWords = [...correctWords, ...decoys];
    const word = allWords[Math.floor(Math.random() * allWords.length)];
    
    const screenWidth = typeof window !== 'undefined' ? window.innerWidth : 1200;
    const shipTypes: ('galleon' | 'frigate' | 'sloop')[] = ['galleon', 'frigate', 'sloop'];

    // Find a non-overlapping position for the new ship
    const existingShipPositions = existingShips.map(s => ({ x: s.x, y: s.y }));
    const position = themeEngine.current.findNonOverlappingPosition(
      existingShipPositions,
      screenWidth,
      800,
      160
    );

    return {
      id: `ship-${Date.now()}-${Math.random()}`,
      word,
      isCorrect: correctWords.includes(word),
      x: position.x,
      y: -200 + initialYOffset,
      speed: (0.6 + Math.random() * 0.3) * 2,
      rotation: (Math.random() - 0.5) * 5,
      scale: 0.8 + Math.random() * 0.3,
      spawnTime: Date.now(),
      clicked: false,
      shipType: shipTypes[Math.floor(Math.random() * shipTypes.length)],
      sailsUp: Math.random() > 0.3,
      treasureValue: Math.floor(Math.random() * 100) + 50,
      weathered: Math.random() * 0.5 + 0.3,
      floating: true,
      feedbackStatus: undefined
    };
  };

  // Spawn initial set of pirate ships when challenge changes or unpauses
  const spawnInitialPirateShips = () => {
    if (!currentChallenge) return;

    const initialShips: PirateShip[] = [];
    for (let i = 0; i < MAX_ACTIVE_SHIPS; i++) {
      const newShip = createSingleShip(initialShips, -i * 180);
      if (newShip) initialShips.push(newShip);
    }
    setPirateShips(initialShips);
  };

  // Update ship positions and floating motion
  const updateShips = () => {
    if (isPaused || !gameActive) return;

    const screenHeight = typeof window !== 'undefined' ? window.innerHeight : 800;
    const screenWidth = typeof window !== 'undefined' ? window.innerWidth : 1200;
    const shipsToRemove: PirateShip[] = [];

    setPirateShips(prev =>
      prev.map(ship => {
        let newY = ship.y + ship.speed;
        let newX = ship.x + Math.sin(Date.now() * 0.002 + ship.spawnTime * 0.001) * 0.3;

        if (newY > screenHeight + 50) {
          shipsToRemove.push(ship);
          return { ...ship, y: newY };
        }

        return {
          ...ship,
          y: newY,
          x: newX,
          rotation: ship.rotation + Math.sin(Date.now() * 0.003) * 0.5
        };
      }).filter(ship => {
        const shouldRemove = shipsToRemove.some(s => s.id === ship.id);
        if (shouldRemove) {
          const explosionParticles = themeEngine.current.createParticleEffect(ship.x, ship.y, 'error', 20);
          setParticles(currentParticles => [...currentParticles, ...explosionParticles]);
          createWaterSplash(ship.x, ship.y);
        }
        return !shouldRemove;
      })
    );

    // Regenerate new ships if below threshold
    if (pirateShips.length - shipsToRemove.length < MAX_ACTIVE_SHIPS) {
      const newShip = createSingleShip(pirateShips, 0);
      if (newShip) {
        setPirateShips(prev => [...prev, newShip]);
      }
    }
  };

  // Update cannon balls
  const updateCannonBalls = () => {
    setCannonBalls(prev =>
      prev.map(ball => ({
        ...ball,
        progress: Math.min(ball.progress + 0.05, 1),
        x: ball.x + (ball.targetX - ball.x) * 0.05,
        y: ball.y + (ball.targetY - ball.y) * 0.05
      })).filter(ball => ball.progress < 1)
    );
  };

  // Handle ship click (cannon fire)
  const handleShipClick = (ship: PirateShip) => {
    if (ship.feedbackStatus === 'incorrect' || ship.clicked) return;

    const expectedWord = currentChallenge.words[currentWordIndex];
    const isClickValidAndInOrder = ship.isCorrect && (ship.word === expectedWord);

    const cannonBall: CannonBall = {
      id: `cannon-${Date.now()}`,
      x: 50,
      y: window.innerHeight - 100,
      targetX: ship.x,
      targetY: ship.y,
      progress: 0
    };
    setCannonBalls(prev => [...prev, cannonBall]);

    setTimeout(() => {
      if (isClickValidAndInOrder) {
        setPirateShips(prev =>
          prev.map(s => s.id === ship.id ? { ...s, clicked: true } : s)
        );

        const newWordsCollected = [...wordsCollected, ship.word];
        setWordsCollected(newWordsCollected);
        setCurrentWordIndex(prev => prev + 1);
        
        const successParticles = themeEngine.current.createParticleEffect(
          ship.x, ship.y, 'success', 20
        );
        setParticles(prev => [...prev, ...successParticles]);
        
        createWaterSplash(ship.x, ship.y);
        
        playSFX('gem');
        
        const sequenceBonus = currentWordIndex * 2;
        onCorrectAnswer(10 + sequenceBonus + (difficulty === 'advanced' ? 5 : 0));
        
        if (newWordsCollected.length === currentChallenge.words.length) {
          setTimeout(() => {
            onChallengeComplete();
            setWordsCollected([]);
            setChallengeProgress(0);
            setCurrentWordIndex(0);
          }, 500);
        } else {
          setChallengeProgress(newWordsCollected.length / currentChallenge.words.length);
        }

        setTimeout(() => {
          setPirateShips(prev => prev.filter(s => s.id !== ship.id));
        }, 300);

      } else if (ship.isCorrect && !isClickValidAndInOrder) { 
        setPirateShips(prev =>
          prev.map(s => s.id === ship.id ? { ...s, feedbackStatus: 'incorrect' } : s)
        );

        const warningParticles = themeEngine.current.createParticleEffect(
          ship.x, ship.y, 'ambient', 12
        );
        setParticles(prev => [...prev, ...warningParticles]);
        
        playSFX('wrong-answer');
        onCorrectAnswer(-2);

        setTimeout(() => {
          setPirateShips(prev =>
            prev.map(s => s.id === ship.id ? { ...s, feedbackStatus: undefined } : s)
          );
        }, 500); 

      } else {
        setPirateShips(prev =>
          prev.map(s => s.id === ship.id ? { ...s, feedbackStatus: 'incorrect' } : s)
        );
        
        const errorParticles = themeEngine.current.createParticleEffect(
          ship.x, ship.y, 'error', 12
        );
        setParticles(prev => [...prev, ...errorParticles]);
        
        playSFX('wrong-answer');
        onIncorrectAnswer();

        setTimeout(() => {
          setPirateShips(prev =>
            prev.map(s => s.id === ship.id ? { ...s, feedbackStatus: undefined } : s)
          );
        }, 500); 
      }
    }, 800);
  };

  const createWaterSplash = (x: number, y: number) => {
    const splashParticles = Array.from({ length: 15 }, (_, i) => ({
      id: crypto.randomUUID(), // Fix: Use unique ID to prevent key warnings
      x,
      y,
      vx: (Math.random() - 0.5) * 12,
      vy: -Math.random() * 8 - 3,
      life: 1,
      maxLife: 1,
      color: '#3B82F6',
      size: Math.random() * 6 + 3,
      type: 'ambient' as const
    }));

    setParticles(prev => [...prev, ...splashParticles]);
  };

  const updateParticles = () => {
    setParticles(prev =>
      prev.map(p => ({
        ...p,
        x: p.x + p.vx,
        y: p.y + p.vy,
        life: p.life - 0.02
      })).filter(p => p.life > 0)
    );
  };

  const updateLavaParticles = () => {
    // This function is not used in PirateAdventureEngine, but is part of the original template
    // Leaving it in as a placeholder in case it's needed for other effects
    return;
  };

  useEffect(() => {
    const animate = () => {
      updateShips();
      updateCannonBalls();
      updateParticles();
      updateLavaParticles(); // Call the placeholder
      animationRef.current = requestAnimationFrame(animate);
    };

    if (gameActive) {
      animationRef.current = requestAnimationFrame(animate);
    }

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [gameActive, isPaused, pirateShips.length]);

  useEffect(() => {
    if (currentChallenge && gameActive) {
      setWordsCollected([]);
      setChallengeProgress(0);
      setCurrentWordIndex(0);
      spawnInitialPirateShips();
    }
  }, [currentChallenge, gameActive]);

  useEffect(() => {
    if (
      currentChallenge &&
      gameActive &&
      !isPaused &&
      pirateShips.length < MAX_ACTIVE_SHIPS
    ) {
      const numToSpawn = MAX_ACTIVE_SHIPS - pirateShips.length;
      if (numToSpawn > 0) {
        const newShipsToAdd: PirateShip[] = [];
        for (let i = 0; i < numToSpawn; i++) {
          const allCurrentShips = [...pirateShips, ...newShipsToAdd];
          const newShip = createSingleShip(allCurrentShips, -i * 180);
          if (newShip) newShipsToAdd.push(newShip);
        }
        setPirateShips(prev => [...prev, ...newShipsToAdd]);
      }
    }
  }, [isPaused, gameActive, currentChallenge, pirateShips.length]);


  if (!currentChallenge) {
    return (
      <div className="flex items-center justify-center h-full w-full bg-slate-900 text-blue-200 text-xl">
        No challenge loaded. Please start a game or check your data source.
      </div>
    );
  }

  return (
    <div
      ref={containerRef}
      className="absolute inset-0 overflow-hidden"
    >
      {/* Video Background */}
      <div className="absolute inset-0">
        <video
          className="absolute inset-0 w-full h-full object-cover"
          autoPlay
          muted
          loop
          playsInline
        >
          <source src="/games/noughts-and-crosses/images/pirate-adventure/pirate-adventure-bg.mp4" type="video/mp4" />
        </video>

        {/* Video overlay gradient for better readability */}
        <div className="absolute inset-0 bg-gradient-to-b from-black/30 via-transparent to-black/50"></div>
      </div>

      {/* English Sentence Display */}
      <div className="absolute top-40 left-1/2 transform -translate-x-1/2 z-50">

        <div className="bg-black/70 backdrop-blur-sm rounded-lg px-6 py-4 border border-amber-500/30">
          <div className="text-center">
            <div className="text-sm text-amber-300 mb-1">🏴‍☠️ Decode this treasure map:</div>
            <div className="text-xl font-bold text-white">{currentChallenge.english}</div>
            <div className="text-sm text-blue-300 mt-2">
              Click the {currentChallenge.targetLanguage} pirate ships in the correct order
            </div>
          </div>
        </div>
      </div>

      {/* Progress Display */}
      <div className="absolute top-24 left-1/2 transform -translate-x-1/2 z-50">
        <div className="bg-black/50 backdrop-blur-sm rounded-lg px-4 py-2 border border-amber-500/20">
          <div className="text-center">
            <div className="text-sm text-amber-300">Treasure Map:</div>
            <div className="text-lg font-bold text-white">
              {wordsCollected.join(' ')}
            </div>
          </div>
        </div>
      </div>

      {/* Ocean Background */}
      <div className="absolute inset-0">
        {/* Seagulls */}
        {Array.from({ length: 3 }, (_, i) => (
          <motion.div
            key={i}
            className="absolute text-white text-sm"
            animate={{
              x: [-20, window.innerWidth + 20],
              y: [Math.random() * 100 + 50, Math.random() * 100 + 100]
            }}
            transition={{
              duration: Math.random() * 15 + 20,
              repeat: Infinity,
              ease: 'linear',
              delay: Math.random() * 10
            }}
          >
            🕊️
          </motion.div>
        ))}
      </div>

      {/* Pirate Ships */}
      <AnimatePresence>
        {pirateShips.map((ship) => (
          <PirateShipComponent
            key={ship.id}
            ship={ship}
            onClick={() => handleShipClick(ship)}
          />
        ))}
      </AnimatePresence>

      {/* Cannon Balls */}
      <AnimatePresence>
        {cannonBalls.map((ball) => (
          <motion.div
            key={ball.id}
            className="absolute w-4 h-4 bg-gray-800 rounded-full shadow-lg"
            style={{ x: ball.x, y: ball.y }}
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            exit={{ scale: 0 }}
          />
        ))}
      </AnimatePresence>

      {/* Particle Effects */}
      <AnimatePresence>
        {particles.map((particle) => (
          <motion.div
            key={particle.id}
            initial={{ opacity: 1, scale: 1, x: particle.x, y: particle.y }}
            animate={{
              opacity: 0,
              scale: 0,
              x: particle.x + particle.vx * 50,
              y: particle.y + particle.vy * 50
            }}
            exit={{ opacity: 0 }}
            transition={{ duration: 1.5 }}
            className="absolute w-2 h-2 rounded-full pointer-events-none"
            style={{
              backgroundColor: particle.color,
              width: particle.size,
              height: particle.size
            }}
          />
        ))}
      </AnimatePresence>
    </div>
  );
}

// Pirate Ship Component
const PirateShipComponent: React.FC<{
  ship: PirateShip;
  onClick: () => void;
}> = ({ ship, onClick }) => {
  const shipEmojis = {
    galleon: '🚢',
    frigate: '⛵',
    sloop: '🛥️'
  };

  const getBorderClass = () => {
    if (ship.feedbackStatus === 'incorrect') {
      return 'border-red-500 ring-4 ring-red-500';
    }
    return 'border-amber-800';
  };

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0, x: ship.x, y: ship.y, rotate: ship.rotation }}
      animate={{
        opacity: ship.clicked ? 0 : 1,
        scale: ship.clicked ? 0.5 : ship.scale,
        x: ship.x,
        y: ship.y,
        rotate: ship.rotation
      }}
      exit={{ opacity: 0, scale: 0 }}
      onClick={onClick}
      className="absolute cursor-pointer transition-all duration-200 hover:scale-110 select-none"
    >
      <div className={`bg-amber-100 border-2 ${getBorderClass()} rounded-lg px-4 py-3 shadow-lg backdrop-blur-sm min-w-[160px] text-center relative overflow-hidden`}>
        <div className="absolute inset-0 bg-gradient-to-br from-amber-200/50 to-amber-800/30" />
        <div className="relative z-10">
          <div className="flex items-center justify-center gap-2 mb-1">
            <span className="text-2xl">{shipEmojis[ship.shipType]}</span>
            <span className="text-xs text-amber-700">
              {ship.sailsUp ? '⛵' : '🏴‍☠️'}
            </span>
          </div>
          <div className="font-bold text-amber-900 text-lg drop-shadow-sm">
            {ship.word}
          </div>
          <div className="text-xs text-amber-700 mt-1">
            💰 {ship.treasureValue} doubloons
          </div>
        </div>
        <div className="absolute inset-0 rounded-lg border-2 border-amber-600/50" />
      </div>
    </motion.div>
  );
};

// Theme engine implementation
class PirateAdventureThemeEngine extends BaseThemeEngine {
  // Inherits all base functionality
}
