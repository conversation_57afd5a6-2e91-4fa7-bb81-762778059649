'use client';

import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { WordBlastEngineProps } from '../WordBlastEngine';
import { WordObject, ParticleEffect, BaseThemeEngine } from '../BaseThemeEngine';

// Updated StoneTablet interface to include feedbackStatus and clicked property for consistent behavior
interface StoneTablet extends WordObject {
  stoneType: 'granite' | 'marble' | 'obsidian';
  crackLevel: number;
  lavaProximity: number;
  runeGlow: number;
  cracking: boolean;
  clicked: boolean; // Added: To control permanent disappearance
  feedbackStatus?: 'incorrect'; // Added: For temporary visual feedback on incorrect clicks
}

interface LavaParticle {
  id: string;
  x: number;
  y: number;
  vx: number;
  vy: number;
  life: number;
  size: number;
  temperature: number;
}

// Define a maximum number of active tablets to maintain game flow
const MAX_ACTIVE_TABLETS = 8; // You can adjust this number

export default function LavaTempleEngine(props: WordBlastEngineProps) {
  const {
    currentChallenge,
    challenges,
    onCorrectAnswer,
    onIncorrectAnswer,
    onChallengeComplete,
    isPaused,
    gameActive,
    difficulty,
    playSFX
  } = props;

  const [stoneTablets, setStoneTablets] = useState<StoneTablet[]>([]);
  const [particles, setParticles] = useState<ParticleEffect[]>([]);
  const [lavaParticles, setLavaParticles] = useState<LavaParticle[]>([]);
  // Removed emberEffects as it's not used in this theme
  const [wordsCollected, setWordsCollected] = useState<string[]>([]);
  const [challengeProgress, setChallengeProgress] = useState(0);
  const [currentWordIndex, setCurrentWordIndex] = useState(0);

  // New state to track all words that should be cycling
  const [activeWordPool, setActiveWordPool] = useState<string[]>([]);

  const containerRef = useRef<HTMLDivElement>(null);
  const animationRef = useRef<number>();
  const themeEngine = useRef(new LavaTempleThemeEngine());

  // Initialize the word pool when challenge changes
  useEffect(() => {
    if (currentChallenge) {
      const correctWords = currentChallenge.words;
      const decoys = themeEngine.current.generateDecoys(correctWords, challenges, difficulty);
      const allWords = [...correctWords, ...decoys];
      setActiveWordPool(allWords);
    }
  }, [currentChallenge, challenges, difficulty]);

  // Helper function to create a single new tablet with specific word
  const createSingleTablet = (word: string, initialYOffset: number = 0, existingTablets?: StoneTablet[]) => {
    if (!currentChallenge || !activeWordPool.length) return null;

    const screenWidth = typeof window !== 'undefined' ? window.innerWidth : 1200;
    const stoneTypes: ('granite' | 'marble' | 'obsidian')[] = ['granite', 'marble', 'obsidian'];

    // Use provided tablets or get current state
    const tabletsToCheck = existingTablets || stoneTablets;

    // Find a non-overlapping position for the new tablet
    const position = themeEngine.current.findNonOverlappingPosition(
      tabletsToCheck,
      screenWidth,
      800, // Max Y for new tablets, assuming 800px height for calculation
      150
    );

    const timestamp = Date.now();
    const randomSuffix = Math.random().toString(36).substr(2, 9);

    return {
      id: `tablet-${timestamp}-${randomSuffix}`,
      word,
      isCorrect: currentChallenge.words.includes(word),
      x: position.x,
      y: -150 + initialYOffset,
      speed: (0.5 + Math.random() * 0.3) * 2,
      rotation: 0,
      scale: 0.9 + Math.random() * 0.2,
      spawnTime: Date.now(),
      clicked: false,
      stoneType: stoneTypes[Math.floor(Math.random() * stoneTypes.length)],
      crackLevel: Math.random() * 0.2,
      lavaProximity: 0,
      runeGlow: Math.random() * 0.5 + 0.5,
      cracking: false,
      feedbackStatus: undefined
    };
  };

  // Helper function to get next word from pool (cycling through all words)
  const getNextWordFromPool = (currentTablets?: StoneTablet[]): string | null => {
    if (!activeWordPool.length) return null;

    // Use provided tablets or get current state
    const tabletsToCheck = currentTablets || stoneTablets;

    // Get words that are not currently on screen
    const wordsOnScreen = tabletsToCheck.map(t => t.word);
    const availableWords = activeWordPool.filter(word => !wordsOnScreen.includes(word));

    // If all words are on screen, pick randomly from the pool
    if (availableWords.length === 0) {
      return activeWordPool[Math.floor(Math.random() * activeWordPool.length)];
    }

    // Otherwise, pick from available words
    return availableWords[Math.floor(Math.random() * availableWords.length)];
  };

  // Spawn initial set of stone tablets when challenge changes or unpauses
  const spawnInitialTablets = () => {
    if (!currentChallenge || !activeWordPool.length) return;

    const initialTablets: StoneTablet[] = [];
    // Spawn MAX_ACTIVE_TABLETS on initial load, slightly staggered
    for (let i = 0; i < Math.min(MAX_ACTIVE_TABLETS, activeWordPool.length); i++) {
      const word = getNextWordFromPool(initialTablets);
      if (word) {
        const newTablet = createSingleTablet(word, -i * 150, initialTablets);
        if (newTablet) initialTablets.push(newTablet);
      }
    }
    setStoneTablets(initialTablets);
  };

  // Update tablet positions and effects
  const updateTablets = () => {
    if (isPaused || !gameActive) return;

    const screenHeight = typeof window !== 'undefined' ? window.innerHeight : 800;

    setStoneTablets(prev => {
      const tabletsToKeep: StoneTablet[] = [];
      const tabletsToExplode: StoneTablet[] = [];

      // Process each tablet
      prev.forEach(tablet => {
        // Skip tablets that are already clicked (they're handled by handleTabletClick)
        if (tablet.clicked) {
          tabletsToKeep.push(tablet);
          return;
        }

        const newY = tablet.y + tablet.speed;

        // Check if tablet hit the bottom
        if (newY > screenHeight + 50) {
          // Mark this tablet for explosion
          tabletsToExplode.push(tablet);
          return;
        }

        // Update tablet position and properties
        const lavaProximity = Math.max(0, (newY - screenHeight * 0.6) / (screenHeight * 0.4));

        tabletsToKeep.push({
          ...tablet,
          y: newY,
          lavaProximity,
          runeGlow: tablet.runeGlow + Math.sin(Date.now() * 0.005) * 0.1,
          rotation: 0
        });
      });

      // Handle explosions for tablets that hit the ground
      tabletsToExplode.forEach(tablet => {
        // Trigger explosion particles
        const explosionParticles = themeEngine.current.createParticleEffect(
          tablet.x, tablet.y, 'error', 15
        );
        setParticles(prevParticles => [...prevParticles, ...explosionParticles]);
        createLavaEruption(tablet.x, tablet.y);

        // Schedule a respawn for a new tablet
        setTimeout(() => {
          setStoneTablets(current => {
            const wordToRespawn = getNextWordFromPool(current);
            if (wordToRespawn) {
              const newTablet = createSingleTablet(wordToRespawn, 0, current);
              if (newTablet) {
                return [...current, newTablet];
              }
            }
            return current;
          });
        }, Math.random() * 1000 + 500); // Random delay between 0.5-1.5 seconds
      });

      return tabletsToKeep;
    });
  };

  // Handle tablet click
  const handleTabletClick = (tablet: StoneTablet) => {
    // Prevent re-clicks if a tablet is already processing feedback or has been permanently clicked
    if (tablet.feedbackStatus === 'incorrect' || tablet.clicked) return;

    const expectedWord = currentChallenge.words[currentWordIndex];
    const isClickValidAndInOrder = tablet.isCorrect && (tablet.word === expectedWord);

    if (isClickValidAndInOrder) {
      // Scenario 1: Correct word clicked IN THE CORRECT ORDER
      // This is the ONLY time tablets disappear and progress advances.
      setStoneTablets(prev =>
        prev.map(t => t.id === tablet.id ? { ...t, clicked: true, cracking: true } : t)
      );

      const newWordsCollected = [...wordsCollected, tablet.word];
      setWordsCollected(newWordsCollected);
      setCurrentWordIndex(prev => prev + 1);

      const successParticles = themeEngine.current.createParticleEffect(
        tablet.x, tablet.y, 'success', 15
      );
      setParticles(prev => [...prev, ...successParticles]);

      // Create lava eruption effect
      createLavaEruption(tablet.x, tablet.y);

      playSFX('gem');

      const sequenceBonus = currentWordIndex * 2;
      onCorrectAnswer(10 + sequenceBonus + (difficulty === 'advanced' ? 5 : 0));

      // Check if challenge is complete
      if (newWordsCollected.length === currentChallenge.words.length) {
        setTimeout(() => {
          onChallengeComplete();
          // Progress bar fully resets ONLY when challenge is complete/new one loads
          setWordsCollected([]);
          setChallengeProgress(0);
          setCurrentWordIndex(0);
        }, 500);
      } else {
        setChallengeProgress(newWordsCollected.length / currentChallenge.words.length);
      }

      // Remove clicked tablet after a short delay to allow for animation
      setTimeout(() => {
        setStoneTablets(prev => prev.filter(t => t.id !== tablet.id));

        // Respawn a new tablet for the same word to maintain pool size
        setTimeout(() => {
          setStoneTablets(current => {
            const newTablet = createSingleTablet(tablet.word, 0, current);
            if (newTablet) {
              return [...current, newTablet];
            }
            return current;
          });
        }, Math.random() * 2000 + 1000); // 1-3 second delay
      }, 300); // Short delay for click animation before removal

    } else if (tablet.isCorrect && !isClickValidAndInOrder) {
      // Scenario 2: Correct word clicked OUT OF ORDER
      // Tablet stays, flashes red. PROGRESS DOES NOT RESET.
      setStoneTablets(prev =>
        prev.map(t => t.id === tablet.id ? { ...t, feedbackStatus: 'incorrect', cracking: true } : t)
      );

      const warningParticles = themeEngine.current.createParticleEffect(
        tablet.x, tablet.y, 'ambient', 8
      );
      setParticles(prev => [...prev, ...warningParticles]);

      // Play wrong-answer sound for out-of-order
      playSFX('wrong-answer');
      onCorrectAnswer(-2); // Small point deduction

      // Clear the feedbackStatus and cracking after a short delay
      setTimeout(() => {
        setStoneTablets(prev =>
          prev.map(t => t.id === tablet.id ? { ...t, feedbackStatus: undefined, cracking: false } : t)
        );
      }, 500);

    } else {
      // Scenario 3: Decoy word clicked (NOT part of the correct sentence at all)
      // Tablet stays, flashes red. PROGRESS DOES NOT RESET.
      setStoneTablets(prev =>
        prev.map(t => t.id === tablet.id ? { ...t, feedbackStatus: 'incorrect', cracking: true } : t)
      );

      const errorParticles = themeEngine.current.createParticleEffect(
        tablet.x, tablet.y, 'error', 8
      );
      setParticles(prev => [...prev, ...errorParticles]);

      // Play wrong-answer sound for decoys
      playSFX('wrong-answer');
      onIncorrectAnswer(); // This reduces health/lives

      // Clear the feedbackStatus and cracking after a short delay
      setTimeout(() => {
        setStoneTablets(prev =>
          prev.map(t => t.id === tablet.id ? { ...t, feedbackStatus: undefined, cracking: false } : t)
        );
      }, 500);
    }
  };

  // Create lava eruption effect (this seems specific to LavaTemple theme)
  const createLavaEruption = (x: number, y: number) => {
    const lavaParticleCount = 20;
    const timestamp = Date.now();
    const randomSuffix = Math.random().toString(36).substr(2, 9); // Generate random string
    
    const newLavaParticles: LavaParticle[] = Array.from({ length: lavaParticleCount }, (_, i) => ({
      id: `lava-${timestamp}-${randomSuffix}-${i}`, // More unique ID
      x,
      y,
      vx: (Math.random() - 0.5) * 15,
      vy: -Math.random() * 10 - 5,
      life: 1,
      size: Math.random() * 8 + 4,
      temperature: Math.random() * 0.5 + 0.5
    }));

    setLavaParticles(prev => [...prev, ...newLavaParticles]);

    // Remove lava particles after animation
    setTimeout(() => {
      setLavaParticles(prev =>
        prev.filter(p => !newLavaParticles.some(np => np.id === p.id))
      );
    }, 2000);
  };

  // Animation loop
  useEffect(() => {
    const animate = () => {
      if (!isPaused && gameActive) {
        updateTablets();
      }
      // Update general particles (they self-remove via AnimatePresence exit prop)
      // Update lava particles (they self-remove via AnimatePresence exit prop and setTimeout in createLavaEruption)
      animationRef.current = requestAnimationFrame(animate);
    };

    if (gameActive) {
      animationRef.current = requestAnimationFrame(animate);
    }

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [gameActive, isPaused]); // Removed stoneTablets.length from deps to avoid re-running on every tablet change

  // Spawn initial tablets when challenge changes (also handles initial spawn)
  useEffect(() => {
    if (currentChallenge && gameActive && activeWordPool.length > 0) {
      setWordsCollected([]);
      setChallengeProgress(0);
      setCurrentWordIndex(0);
      setStoneTablets([]); // Clear existing tablets to prevent accumulation
      spawnInitialTablets();
    }
  }, [currentChallenge, gameActive, activeWordPool]);

  // Maintain tablet count when unpausing or after a tablet is removed
  useEffect(() => {
    const minTablets = Math.min(MAX_ACTIVE_TABLETS, activeWordPool.length);
    if (
      currentChallenge &&
      gameActive &&
      !isPaused &&
      activeWordPool.length > 0 &&
      stoneTablets.length < minTablets
    ) {
      const numToSpawn = minTablets - stoneTablets.length;
      if (numToSpawn > 0) {
        setStoneTablets(prev => {
          const newTabletsToAdd: StoneTablet[] = [];
          for (let i = 0; i < numToSpawn; i++) {
            const word = getNextWordFromPool([...prev, ...newTabletsToAdd]);
            if (word) {
              const newTablet = createSingleTablet(word, 0, [...prev, ...newTabletsToAdd]);
              if (newTablet) newTabletsToAdd.push(newTablet);
            }
          }
          return [...prev, ...newTabletsToAdd];
        });
      }
    }
  }, [isPaused, gameActive, currentChallenge, stoneTablets.length, activeWordPool]);

  // Clean up particles when component unmounts or game ends
  useEffect(() => {
    if (!gameActive) {
      setParticles([]);
      setLavaParticles([]);
    }
  }, [gameActive]);


  if (!currentChallenge) {
    return (
      <div className="flex items-center justify-center h-full w-full bg-slate-900 text-blue-200 text-xl">
        No challenge loaded. Please start a game or check your data source.
      </div>
    );
  }

  return (
    <div
      ref={containerRef}
      className="absolute inset-0 overflow-hidden"
    >
      {/* Video Background */}
      <div className="absolute inset-0">
        <video
          className="absolute inset-0 w-full h-full object-cover"
          autoPlay
          muted
          loop
          playsInline
        >
          <source src="/games/noughts-and-crosses/images/lava-temple/lava-temple-bg.mp4" type="video/mp4" />
        </video>

        {/* Video overlay gradient for better readability */}
        <div className="absolute inset-0 bg-gradient-to-b from-black/30 via-transparent to-black/50"></div>
      </div>

      {/* English Sentence Display */}
      <div className="absolute top-40 left-1/2 transform -translate-x-1/2 z-50">
        <div className="bg-black/70 backdrop-blur-sm rounded-lg px-6 py-4 border border-orange-500/30">
          <div className="text-center">
            <div className="text-sm text-orange-300 mb-1">🔥 Translate this ancient text:</div>
            <div className="text-xl font-bold text-white">{currentChallenge.english}</div>
            <div className="text-sm text-orange-300 mt-2">
              Click the {currentChallenge.targetLanguage} stone tablets in the correct order
            </div>
          </div>
        </div>
      </div>

      {/* Progress Display */}
      <div className="absolute top-24 left-1/2 transform -translate-x-1/2 z-50">
        <div className="bg-black/50 backdrop-blur-sm rounded-lg px-4 py-2 border border-white/10">
          <div className="text-center">
            <div className="text-sm text-gray-300">Progress:</div>
            <div className="text-lg font-bold text-white">
              {wordsCollected.join(' ')}
            </div>
          </div>
        </div>
      </div>

      {/* Lava Temple Background Effects */}
      <div className="absolute inset-0">
        {/* Temple pillars */}
        <div className="absolute left-10 top-0 w-16 h-full bg-gradient-to-b from-amber-900 to-orange-800 opacity-30" />
        <div className="absolute right-10 top-0 w-16 h-full bg-gradient-to-b from-amber-900 to-orange-800 opacity-30" />

        {/* Lava glow at bottom */}
        <div className="absolute bottom-0 left-0 right-0 h-32 bg-gradient-to-t from-red-600 via-orange-500 to-transparent opacity-60" />
      </div>

      {/* Stone Tablets */}
      <AnimatePresence>
        {stoneTablets.map((tablet) => (
          <StoneTabletComponent
            key={tablet.id}
            tablet={tablet}
            onClick={() => handleTabletClick(tablet)}
          />
        ))}
      </AnimatePresence>

      {/* Particle Effects (General) */}
      <AnimatePresence>
        {particles.map((particle) => (
          <motion.div
            key={particle.id}
            initial={{ opacity: 1, scale: 1, x: particle.x, y: particle.y }}
            animate={{
              opacity: 0,
              scale: 0,
              x: particle.x + particle.vx * 50,
              y: particle.y + particle.vy * 50
            }}
            exit={{ opacity: 0 }}
            transition={{ duration: 1 }}
            className="absolute w-2 h-2 rounded-full pointer-events-none"
            style={{
              backgroundColor: particle.color,
              width: particle.size,
              height: particle.size
            }}
          />
        ))}
      </AnimatePresence>

      {/* Lava Particles (Specific to Lava Temple) */}
      <AnimatePresence>
        {lavaParticles.map((particle) => (
          <motion.div
            key={particle.id}
            initial={{ opacity: particle.temperature, scale: particle.size / 10 }}
            animate={{
              x: particle.x + particle.vx,
              y: particle.y + particle.vy,
              opacity: 0,
              scale: 0
            }}
            transition={{ duration: particle.life * 2, ease: "easeOut" }}
            className="absolute rounded-full bg-orange-500 shadow-lg shadow-orange-500/50"
            style={{
              left: particle.x,
              top: particle.y,
              width: particle.size,
              height: particle.size,
              filter: `blur(${particle.size / 4}px)`
            }}
          />
        ))}
      </AnimatePresence>
    </div>
  );
}

// Stone Tablet Component
const StoneTabletComponent: React.FC<{
  tablet: StoneTablet;
  onClick: () => void;
}> = ({ tablet, onClick }) => {
  const stoneColors = {
    granite: 'from-gray-600 to-gray-800',
    marble: 'from-gray-300 to-gray-500',
    obsidian: 'from-gray-900 to-black'
  };

  // Helper functions for dynamic styling based on feedbackStatus
  const getBorderClass = () => {
    if (tablet.feedbackStatus === 'incorrect') {
      return 'border-red-500 ring-4 ring-red-500'; // Make it stand out with a red border/ring
    }
    // Original border for stone tablets
    return 'border-amber-600';
  };

  const getShadowClass = () => {
    if (tablet.feedbackStatus === 'incorrect') {
      return 'shadow-red-500/80'; // Red shadow for incorrect feedback
    }
    // Original shadow for stone tablets, combining with lava glow
    return `shadow-2xl shadow-orange-400/${Math.round(tablet.lavaProximity * 50) + 30}`;
  };

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0, x: tablet.x, y: tablet.y, rotate: tablet.rotation }}
      animate={{
        opacity: tablet.clicked ? 0 : 1, // Only disappear if 'clicked' (for correct tablets)
        scale: tablet.clicked ? 0.5 : tablet.scale,
        x: tablet.x,
        y: tablet.y,
        rotate: tablet.rotation
      }}
      exit={{ opacity: 0, scale: 0 }}
      onClick={onClick}
      // Add 'transition-all' for a smooth color change for feedbackStatus
      className={`absolute cursor-pointer transition-all duration-200 hover:scale-110 select-none`}
      style={{
        filter: `brightness(${1 + tablet.lavaProximity * 0.5}) drop-shadow(0 0 ${tablet.runeGlow * 10}px rgba(255, 140, 0, 0.8))`
      }}
    >
      <div className={`bg-gradient-to-br ${stoneColors[tablet.stoneType]} border-2 ${getBorderClass()} ${getShadowClass()} rounded-lg px-4 py-3 min-w-[120px] text-center relative overflow-hidden`}>
        {/* Crack overlay - now also affected by feedbackStatus */}
        {(tablet.cracking || tablet.feedbackStatus === 'incorrect') && (
          <div className="absolute inset-0 bg-gradient-to-br from-transparent via-red-500/30 to-orange-500/50" />
        )}

        {/* Rune-like text */}
        <div className="relative z-10">
          <div className="text-amber-100 font-bold text-lg drop-shadow-lg">
            {tablet.word}
          </div>
        </div>

        {/* Stone texture overlay */}
        <div className="absolute inset-0 opacity-20 bg-gradient-to-br from-white/10 to-transparent" />

        {/* Glow effect */}
        <div
          className="absolute inset-0 rounded-lg border border-orange-400/50"
          style={{
            boxShadow: `inset 0 0 ${tablet.runeGlow * 20}px rgba(255, 140, 0, 0.3)`
          }}
        />
      </div>
    </motion.div>
  );
};

// Theme engine implementation
class LavaTempleThemeEngine extends BaseThemeEngine {
  // Inherits all base functionality
}